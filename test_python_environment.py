#!/usr/bin/env python3
"""
Simple Python Environment Test
Tests basic Python functionality and imports
"""

import sys
import os
from pathlib import Path
from typing import List, Dict, Any

def test_basic_python() -> bool:
    """Test basic Python functionality"""
    print("Testing basic Python functionality...")
    
    # Test basic operations
    result = 2 + 2
    if result == 4:
        print("   SUCCESS: Basic arithmetic working")
    else:
        print("   ERROR: Basic arithmetic failed")
        return False
    
    # Test string operations
    test_string = "Hello World"
    if test_string.upper() == "HELLO WORLD":
        print("   SUCCESS: String operations working")
    else:
        print("   ERROR: String operations failed")
        return False
    
    # Test list operations
    test_list: List[int] = [1, 2, 3, 4, 5]
    if len(test_list) == 5 and sum(test_list) == 15:
        print("   SUCCESS: List operations working")
    else:
        print("   ERROR: List operations failed")
        return False
    
    # Test dictionary operations
    test_dict: Dict[str, Any] = {"key1": "value1", "key2": 42}
    if test_dict.get("key1") == "value1" and test_dict.get("key2") == 42:
        print("   SUCCESS: Dictionary operations working")
    else:
        print("   ERROR: Dictionary operations failed")
        return False
    
    return True

def test_imports() -> bool:
    """Test common imports"""
    print("Testing common imports...")
    
    try:
        import json
        print("   SUCCESS: json import working")
    except ImportError:
        print("   ERROR: json import failed")
        return False
    
    try:
        import datetime
        print("   SUCCESS: datetime import working")
    except ImportError:
        print("   ERROR: datetime import failed")
        return False
    
    try:
        import sqlite3
        print("   SUCCESS: sqlite3 import working")
    except ImportError:
        print("   ERROR: sqlite3 import failed")
        return False
    
    try:
        import asyncio
        print("   SUCCESS: asyncio import working")
    except ImportError:
        print("   ERROR: asyncio import failed")
        return False
    
    return True

def test_file_operations() -> bool:
    """Test file operations"""
    print("Testing file operations...")
    
    try:
        # Test file writing
        test_file = Path("test_file.txt")
        test_file.write_text("Test content")
        print("   SUCCESS: File writing working")
        
        # Test file reading
        content = test_file.read_text()
        if content == "Test content":
            print("   SUCCESS: File reading working")
        else:
            print("   ERROR: File reading failed")
            return False
        
        # Clean up
        test_file.unlink()
        print("   SUCCESS: File cleanup working")
        
    except Exception as e:
        print(f"   ERROR: File operations failed: {e}")
        return False
    
    return True

def test_environment_info() -> None:
    """Display environment information"""
    print("Environment Information:")
    print(f"   Python Version: {sys.version}")
    print(f"   Python Executable: {sys.executable}")
    print(f"   Current Directory: {os.getcwd()}")
    print(f"   Platform: {sys.platform}")

def main() -> bool:
    """Main test function"""
    print("PYTHON ENVIRONMENT TEST")
    print("=" * 50)
    
    # Display environment info
    test_environment_info()
    print()
    
    # Run tests
    tests = [
        test_basic_python,
        test_imports,
        test_file_operations
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            print()
        except Exception as e:
            print(f"   ERROR: Test failed with exception: {e}")
            results.append(False)
            print()
    
    # Summary
    print("=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    if all(results):
        print("SUCCESS: All tests passed!")
        print("Python environment is working correctly.")
        return True
    else:
        print("ERROR: Some tests failed!")
        print("Python environment has issues.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
